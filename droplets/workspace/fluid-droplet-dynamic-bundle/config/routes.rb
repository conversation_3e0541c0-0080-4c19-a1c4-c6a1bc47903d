Rails.application.routes.draw do
  root "home#index"

  devise_for :users

  post "webhook", to: "webhooks#create", as: :webhook
  post "webhooks", to: "webhooks#create", as: :webhooks

  namespace :admin do
    get "dashboard/index"
    resource :droplet, only: %i[ create update ]
    resources :settings, only: %i[ index edit update ]
    resources :users
    resources :callbacks, only: %i[ index show edit update ] do
      post :sync, on: :collection
    end

    # Dynamic Bundle management routes
    resources :bundles do
      member do
        patch :toggle_status
      end

      # Nested categories routes
      resources :categories do
        member do
          patch :move_up
          patch :move_down
        end

        # Nested products routes for assignment
        resources :products, only: [:index] do
          member do
            post :assign
            delete :unassign
            patch :set_default
            patch :move_up
            patch :move_down
          end
        end
      end

      # Export routes
      resource :export, only: [:show, :create], controller: 'exports' do
        member do
          get :preview
          post :validate
          get :download
        end
      end
    end
  end

  get "up" => "rails/health#show", as: :rails_health_check
end
