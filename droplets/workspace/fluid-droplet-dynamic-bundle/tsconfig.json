{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"~/*": ["./app/frontend/*"]}}, "include": ["app/frontend/**/*.ts", "app/frontend/**/*.tsx", "app/frontend/types/**/*.d.ts"], "exclude": ["node_modules"], "references": [{"path": "./tsconfig.node.json"}]}