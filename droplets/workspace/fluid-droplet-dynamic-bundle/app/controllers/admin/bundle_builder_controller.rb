# frozen_string_literal: true

module Admin
  # Bundle Builder Controller - Full-page drag & drop interface
  # Part of the Hybrid Approach for bundle creation
  class BundleBuilderController < ApplicationController
    layout 'bundle_admin'

    before_action :authenticate_admin!
    before_action :ensure_company_context
    before_action :load_bundle_draft, only: [:new, :create]
    before_action :set_bundle, only: [:edit, :update]

    # GET /admin/bundles/new/builder
    # Full-page builder interface for new bundles
    def new
      unless @bundle_draft
        flash[:error] = "No bundle draft found. Please start by creating a bundle."
        redirect_to admin_bundles_path
        return
      end

      @bundle = @bundle_draft
      @categories = []
      @available_products = fetch_available_products
      
      Rails.logger.info("BUILDER: Loading new bundle builder for #{@bundle['name']}")
    end

    # POST /admin/bundles/builder
    # Save the complete bundle configuration
    def create
      unless @bundle_draft
        flash[:error] = "No bundle draft found. Please start by creating a bundle."
        redirect_to admin_bundles_path
        return
      end

      Rails.logger.info("BUILDER: Saving bundle configuration: #{builder_params.inspect}")

      # Process categories and products from the builder
      categories_data = process_categories_data(builder_params[:categories] || [])
      
      # Update bundle with categories
      @bundle_draft['categories'] = categories_data
      @bundle_draft['status'] = 'active' # Activate when saved from builder
      @bundle_draft['updated_at'] = Time.current.iso8601

      # TODO: Save to Fluid API
      # For now, simulate successful save
      
      # Clear the draft from session
      session.delete(:bundle_draft)
      
      flash[:success] = "Bundle '#{@bundle_draft['name']}' created successfully with #{categories_data.length} categories!"
      redirect_to admin_bundles_path
      
      Rails.logger.info("BUILDER: Bundle saved successfully with ID #{@bundle_draft['id']}")
    end

    # GET /admin/bundles/:id/edit/builder
    # Full-page builder interface for existing bundles
    def edit
      @categories = @bundle.dig('metadata', 'categories') || []
      @available_products = fetch_available_products
      
      Rails.logger.info("BUILDER: Loading edit builder for bundle #{@bundle['id']}")
    end

    # PATCH /admin/bundles/:id/builder
    # Update existing bundle configuration
    def update
      Rails.logger.info("BUILDER: Updating bundle #{@bundle['id']}: #{builder_params.inspect}")

      # Process categories and products from the builder
      categories_data = process_categories_data(builder_params[:categories] || [])
      
      # Update bundle metadata
      @bundle['metadata'] ||= {}
      @bundle['metadata']['categories'] = categories_data
      @bundle['updated_at'] = Time.current.iso8601

      # TODO: Update via Fluid API
      # For now, simulate successful update
      
      flash[:success] = "Bundle '#{@bundle['name']}' updated successfully!"
      redirect_to admin_bundles_path
      
      Rails.logger.info("BUILDER: Bundle updated successfully")
    end

    private

    # Load bundle draft from session (for new bundles)
    def load_bundle_draft
      @bundle_draft = session[:bundle_draft]
    end

    # Set bundle for editing (load from API/database)
    def set_bundle
      bundle_id = params[:id] || params[:bundle_id]
      
      # TODO: Load from Fluid API
      # For now, use test data
      @bundle = case bundle_id.to_i
      when 1
        {
          "id" => 1,
          "name" => "Yoli Transformation Bundle",
          "sku" => "YOLI-TRANS-001",
          "description" => "Customizable wellness transformation bundle",
          "status" => "active",
          "metadata" => {
            "categories" => [
              { "id" => 1, "name" => "Supplements", "position" => 1, "products" => [] },
              { "id" => 2, "name" => "Nutrition", "position" => 2, "products" => [] }
            ]
          }
        }
      else
        flash[:error] = "Bundle not found"
        redirect_to admin_bundles_path
        return
      end
    end

    # Fetch available products from Fluid API
    def fetch_available_products
      # TODO: Fetch from Fluid API
      # For now, return test data
      [
        { "id" => 1, "name" => "Protein Powder", "sku" => "PROT-001", "price" => 49.99 },
        { "id" => 2, "name" => "Multivitamin", "sku" => "MULTI-001", "price" => 29.99 },
        { "id" => 3, "name" => "Omega-3", "sku" => "OMEGA-001", "price" => 39.99 },
        { "id" => 4, "name" => "Meal Replacement", "sku" => "MEAL-001", "price" => 59.99 },
        { "id" => 5, "name" => "Energy Drink", "sku" => "ENERGY-001", "price" => 24.99 }
      ]
    end

    # Process categories data from form
    def process_categories_data(categories_params)
      return [] unless categories_params.is_a?(Array)

      categories_params.map.with_index do |category_data, index|
        {
          "id" => category_data[:id] || SecureRandom.uuid,
          "name" => category_data[:name] || "Category #{index + 1}",
          "position" => index + 1,
          "required" => category_data[:required] == "1",
          "max_selections" => category_data[:max_selections]&.to_i || 1,
          "products" => process_products_data(category_data[:products] || [])
        }
      end
    end

    # Process products data for a category
    def process_products_data(products_params)
      return [] unless products_params.is_a?(Array)

      products_params.map.with_index do |product_data, index|
        {
          "id" => product_data[:id],
          "name" => product_data[:name],
          "sku" => product_data[:sku],
          "price" => product_data[:price]&.to_f,
          "position" => index + 1,
          "default" => product_data[:default] == "1"
        }
      end
    end

    # Strong parameters for builder
    def builder_params
      params.permit(
        :bundle_id,
        categories: [
          :id, :name, :required, :max_selections,
          products: [:id, :name, :sku, :price, :default]
        ]
      )
    end

    # Ensure company context is available
    def ensure_company_context
      unless @company
        if Rails.env.development?
          @company = OpenStruct.new(
            fluid_company_id: *********,
            name: "Development Company",
            droplet_installation_uuid: "dev-dri-123",
            authentication_token: "your_real_fluid_token_here"
          )
          Rails.logger.info("HARDCODED: Using development company (ID: #{@company.fluid_company_id})")
          return
        end

        handle_missing_company_context
      end
    end

    # Placeholder for admin authentication
    def authenticate_admin!
      if Rails.env.development?
        Rails.logger.info("DEVELOPMENT: Skipping authentication")
        return true
      end

      redirect_to root_path unless current_user&.admin?
    end
  end
end
