<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= content_for?(:title) ? yield(:title) : "Dynamic Bundle Admin" %></title>
  
  <link rel="icon" href="/icon.png" type="image/png">
  <link rel="icon" href="/icon.svg" type="image/svg+xml">
  <link rel="apple-touch-icon" href="/icon.png">
  
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  
  <%= vite_client_tag %>
  <%= vite_stylesheet_tag 'application' %>
  <%= vite_typescript_tag 'application' %>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f5f7;
    }

    .admin-header {
      background: #2c3e50;
      color: white;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .admin-header h1 {
      font-size: 24px;
      font-weight: 500;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
    }

    /* Tab Navigation */
    .tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
    }

    .tab-nav {
      display: flex;
      border-bottom: 1px solid #e1e5e9;
    }

    .tab-nav a,
    .tab-nav button,
    .tab-nav span {
      background: none;
      border: none;
      padding: 15px 25px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      border-bottom: 3px solid transparent;
      transition: all 0.2s ease;
      text-decoration: none;
      display: inline-block;
    }

    .tab-nav a:hover,
    .tab-nav button:hover {
      background-color: #f8f9fa;
      color: #333;
    }

    .tab-nav a.active,
    .tab-nav button.active {
      color: #007bff;
      border-bottom-color: #007bff;
      background-color: #f8f9ff;
    }

    .tab-nav .disabled {
      color: #adb5bd;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .tab-nav .disabled:hover {
      background-color: transparent;
      color: #adb5bd;
    }

    /* Flash Messages */
    .flash-messages {
      margin-bottom: 20px;
    }

    .flash-message {
      padding: 12px 16px;
      border-radius: 6px;
      margin-bottom: 10px;
      font-weight: 500;
    }

    .flash-message.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .flash-message.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .flash-message.notice {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    /* Content Area */
    .content-area {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 24px;
      min-height: 500px;
    }

    /* Buttons */
    .btn {
      display: inline-block;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;
    }

    .btn-primary {
      background-color: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background-color: #0056b3;
      color: white;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #545b62;
      color: white;
    }

    .btn-success {
      background-color: #28a745;
      color: white;
    }

    .btn-success:hover {
      background-color: #1e7e34;
      color: white;
    }

    .btn-danger {
      background-color: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background-color: #c82333;
      color: white;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
    }

    /* Tables */
    .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .table th,
    .table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e1e5e9;
    }

    .table th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #495057;
    }

    .table tbody tr:hover {
      background-color: #f8f9fa;
    }

    /* Forms */
    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #495057;
    }

    .form-control {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ced4da;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .form-text {
      font-size: 12px;
      color: #6c757d;
      margin-top: 4px;
    }

    /* Utilities */
    .text-center { text-align: center; }
    .text-right { text-align: right; }
    .mb-3 { margin-bottom: 1rem; }
    .mt-3 { margin-top: 1rem; }
    .d-flex { display: flex; }
    .justify-content-between { justify-content: space-between; }
    .align-items-center { align-items: center; }
  </style>
</head>

<body>
  <header class="admin-header">
    <div class="container">
      <h1>Dynamic Bundle Admin</h1>
    </div>
  </header>

  <div class="container">
    <!-- Tab Navigation -->
    <div class="tabs">
      <div class="tab-nav">
        <%= link_to "Bundles", admin_bundles_path,
            class: "#{'active' if controller_name == 'bundles'}" %>
        <% if controller_name == 'categories' && params[:bundle_id] %>
          <%= link_to "Categories", admin_bundle_categories_path(params[:bundle_id]),
              class: "active" %>
        <% else %>
          <span class="disabled">Categories</span>
        <% end %>
        <button>Products</button>
        <button>Settings</button>
      </div>
    </div>

    <!-- Flash Messages -->
    <% if flash.any? %>
      <div class="flash-messages">
        <% flash.each do |type, message| %>
          <div class="flash-message <%= type %>">
            <%= message %>
          </div>
        <% end %>
      </div>
    <% end %>

    <!-- Main Content -->
    <div class="content-area">
      <%= yield %>
    </div>
  </div>

  <!-- Modals and overlays go here -->
  <%= content_for :modals %>
</body>
</html>
