// Type declarations to help with @rjsf/core compatibility issues
declare module '@rjsf/core' {
  import { ComponentType } from 'react';
  import { RJSFSchema, FormProps } from '@rjsf/utils';
  
  const Form: ComponentType<FormProps<any, RJSFSchema, any>>;
  export default Form;
}

// Extend the global namespace to handle some of the type issues
declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}

export {};
